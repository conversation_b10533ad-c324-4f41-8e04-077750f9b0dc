import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { SharedModule } from 'src/app/shared/shared.module';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { AppToasterService, CommonService } from 'src/app/shared/services';
import { CommonModule } from '@angular/common';
import { BlockLocationFormGroup, BlockLocationParams, LocationDetails, LocationDetailsFormGroup, SchoolLocations, SchoolLocationDayOff } from 'src/app/pages/room-and-location-management/models';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { LocationService } from '../../services';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MultiSelectChipsComponent } from 'src/app/shared/components/multi-select-chips/multi-select-chips.component';
import { CBGetResponse, CBResponse, IdNameModel } from 'src/app/shared/models';
import { LocalDatePipe } from 'src/app/shared/pipe';

const DEPENDENCIES = {
  MODULES: [CommonModule, ReactiveFormsModule, MatButtonModule, MatInputModule, MatFormFieldModule, SharedModule, MatDatepickerModule, MatIconModule],
  COMPONENTS: [MultiSelectChipsComponent],
  PIPES: [LocalDatePipe]
};

@Component({
  selector: 'app-block-location',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS, ...DEPENDENCIES.PIPES],
  providers: [provideNativeDateAdapter()],
  templateUrl: './block-location.component.html',
  styleUrl: './block-location.component.scss'
})
export class BlockLocationComponent extends BaseComponent implements OnInit, OnChanges {
  @Input() selectedLocationDetails!: LocationDetails | null;

  locationFormGroup!: FormGroup<BlockLocationFormGroup>;
  locations!: Array<SchoolLocations>;
  locationDayOffList: Array<SchoolLocationDayOff> = [];
  override showPageLoader = false;
  maxDate = new Date();
  blockLocationParams: BlockLocationParams = {
    locationId: {
      id: 1,
      defaultPlaceholder: 'Select Location(s)',
      placeholder: 'Select Location(s)',
      value: [] as Array<IdNameModel>,
      totalCount: 0,
      isOpen: false,
      showSearchBar: true,
      showMax: 1,
      options: [] as Array<IdNameModel>
    }
  };

  @Output() closeSideNav = new EventEmitter<void>();
  @Output() locationAddedOrEdited = new EventEmitter<boolean>();

  constructor(
    private readonly locationService: LocationService,
    private readonly commonService: CommonService,
    private readonly toasterService: AppToasterService,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.initLocationForm();
    this.getAllLocations();
    this.getAllLocationDayOff();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedLocationDetails']?.currentValue) {
      this.selectedLocationDetails = changes['selectedLocationDetails'].currentValue;
      // this.locationFormGroup?.patchValue({ ...this.selectedLocationDetails });
    }
  }

  initLocationForm(): void {
    this.locationFormGroup = new FormGroup<BlockLocationFormGroup>({
      dayOffStartDate: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      dayOffEndDate: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      reason: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      locationIds: new FormControl([], { nonNullable: true }),
      id: new FormControl(undefined, { nonNullable: true })
    });
  }

  getAllLocations(): void {
    this.commonService
      .getLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.blockLocationParams.locationId.options = res.result.items.map(location => ({
            id: location.schoolLocations.id,
            name: location.schoolLocations.locationName
          }));
          this.cdr.detectChanges();
        }
      });
  }

  getAllLocationDayOff(): void {
    this.showPageLoader = true;
    this.locationService
      .getList<CBGetResponse<SchoolLocationDayOff[]>>(API_URL.schoolLocations.getAllSchoolLocationDayOff)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<SchoolLocationDayOff[]>) => {
          this.locationDayOffList = res.result.map((item) => ({
            ...item,
            locationNames: this.getLocationNamesFromIds(item.locationIds)
          }));
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  selectedLocationIds(): void {
    this.locationFormGroup.controls.locationIds.setValue(this.blockLocationParams.locationId.value.map((item) => item.id));
  }

  getLocationNamesFromIds(locationIds: number[]): string {
    const locationArray = []; 
    for (const id of locationIds) {
      console.log('id', id);
      const locationName = this.blockLocationParams.locationId.options.find((item) => item.id === id)?.name;
      console.log(locationName)
      if (locationName) {
        locationArray.push(locationName);
      }
    }
    return locationArray.join(', ');
  }

  onSubmit(): void {
    if (this.locationFormGroup.invalid) {
      this.locationFormGroup.markAllAsTouched();
      return;
    }
    this.locationFormGroup.markAsUntouched();
    this.showBtnLoader = true;
    this.locationService
      .add(this.locationFormGroup.getRawValue(), API_URL.schoolLocations.createOrEditSchoolLocationDayOff)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.showBtnLoader = false;
          this.selectedLocationDetails
            ? this.toasterService.success(this.constants.successMessages.updatedSuccessfully.replace('{item}', 'Location Day Off'))
            : this.toasterService.success(this.constants.successMessages.savedSuccessfully.replace('{item}', 'Location Day Off'));
          // this.locationAddedOrEdited.emit(true);
          this.blockLocationParams.locationId.value = [];
          this.locationFormGroup.reset();
          this.getAllLocationDayOff(); // Refresh the list
          // this.closeSideNavFun();
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  closeSideNavFun(): void {
    this.locationFormGroup.reset();
    this.closeSideNav.emit();
  }
}
