export const API_URL = {
  services: 'services',
  app: 'app',
  account: {
    root: 'Account',
    register: 'Register',
    login: 'Authenticate',
    loginWithThirdParty: 'ExternalAuthenticate',
    tokeAuth: 'TokenAuth',
    refreshAccessToken: 'RefreshToken',
    createUserDetails: 'CreateUserDetails',
    forgotPassword: {
      root: 'reset-password',
      init: 'SendPasswordResetCode',
      finish: 'ResetPassword'
    }
  },
  schoolLocations: {
    root: 'SchoolLocationses',
    createOrEditSchoolLocationDayOff: 'CreateOrEditSchoolLocationDayOff'
  },
  users: {
    root: '',
    paginatedList: 'paginated-sfl-users',
    action: 'sfl-users'
  },
  firebaseSubscription: {
    subscribe: 'subscribe',
    unsubscribe: ''
  },
  linkPreview: {
    apiURL: 'https://api.linkpreview.net',
    apiHeader: 'X-Linkpreview-Api-Key',
    apiHeader<PERSON>ey: '********************************'
  },
  crud: {
    getAll: 'GetAll',
    create: 'Create',
    delete: 'Delete',
    createOrEdit: 'CreateOrEdit',
    update: 'Update',
    createOrUpdate: 'CreateOrUpdate'
  },
  instruments: {
    root: 'InstrumentDetails'
  },
  inquiry: {
    root: 'InquiryDetails'
  },
  subInstruments: {
    root: 'SubInstrumentDetails'
  },
  profile: {
    root: 'Profile',
    getCurrentUserProfileForEdit: 'GetCurrentUserProfileForEdit',
    changePassword: 'ChangePassword'
  },
  introductoryLesson: {
    root: 'IntroductoryLessons'
  },
  instructorDetails: {
    root: 'InstructorDetails',
    getInstructorDetailForView: 'GetInstructorDetailForView',
    getInstructorUnderSupervisor: 'GetInstructorUnderSupervisor',
    getInstructorMonthlyHours: 'GetInstructorMonthlyHours'
  },
  uploadFile: {
    root: 'AWSFileUpload',
    uploadFileToS3: 'UploadFileToS3',
    deleteFileFromAws: 'DeleteFileFromAws',
    fileName: 'fileName',
    downloadFileFromAws: 'DownloadFileFromAws'
  },
  helpCenters: {
    root: 'HelpCenters',
    edit: 'GetHelpCenterForEdit'
  },
  referralDetails: {
    root: 'ReferralDetails',
    createOrEdit: 'CreateOrEdit',
    create: 'Create'
  },
  dependentInformations: {
    root: 'DependentInformations',
    getDependentSchedule: 'GetDependentSchedule',
    assignStudentPlan: 'AssignStudentPlan',
    getDependentInformationForView: 'GetDependentInformationForView',
    updateStudentPlan: 'UpdateStudentPlan',
    getAllStudents: 'GetAllStudents',
    getStudentAttendance: 'GetStudentAttendance',
    getAllAccountManagers: 'GetAllAccountManagers'
  },
  rewardSettings: {
    root: 'RewardSettings',
    getRewardSetting: 'GetRewardSetting'
  },
  documentDetails: {
    root: 'DocumentDetails'
  },
  roomDetails: {
    root: 'RoomDetails',
    getRoomDetailForView: 'GetRoomDetailForView'
  },
  roomInstruments: {
    root: 'RoomInstruments'
  },
  signedDocuments: {
    root: 'SignedDocuments',
    assignAgreementDocument: 'AssignAgreementDocument'
  },
  roomSchedules: 'RoomSchedules',
  groupClassScheduleSummaries: {
    root: 'GroupClassScheduleSummaries',
    getGroupClassScheduleSummaryForView: 'GetGroupClassScheduleSummaryForView'
  },
  scheduleLessonDetails: {
    root: 'ScheduleLessonDetails',
    getScheduleLessonDetailForView: 'GetScheduleLessonDetailForView',
    cancelLesson: 'CancelLesson',
    getCurrentUserScheduleLessonDetail: 'GetCurrentUserScheduleLessonDetail',
    getInstructorAvaibility: 'GetInstructorAvaibility',
    getAllInstructor: 'GetAllInstructor',
    getAvailableIntroductoryLessons: 'GetAvailableIntroductoryLessons',
    bulkCancelScheduleLessons: 'BulkCancelScheduleLessons',
    undoCancelLesson: 'UndoCancelLesson',
    assignMakeUpPass: 'AssignMakeUpPass'
  },
  planSummaries: 'PlanSummaries',
  revenueCategories: {
    root: 'Categories',
    associateCategories: 'GetDataByCategoryId'
  },
  studentPlans: {
    root: 'StudentPlans',
    getStudentPlans: 'GetStudentPlans',
    getStudentPlansAndPasses: 'GetStudentPlansAndPasses',
    cancelStudentAssignedPlan: 'CancelStudentAssignedPlan',
    getAllStudentPlansExpiringSoon: 'GetAllStudentPlansExpiringSoon',
    renewExistingPlan: 'RenewExistingPlan'
  },
  passes: {
    root: 'Passes',
    updateStatus: 'UpdateStatus',
    createMakeUpLessonPass: 'CreateMakeUpLessonPass'
  },
  groupClassEnrollmentDetails: {
    root: 'GroupClassEnrollmentDetails',
    enrollStudent: 'EnrollStudent'
  },
  ensembleClassesScheduleSummaries: {
    root: 'EnsembleClassesScheduleSummaries',
    enrollStudent: 'EnrollStudent',
    getEnsembleClassesScheduleSummaryForView: 'GetEnsembleClassesScheduleSummaryForView',
    getInstructorAvaibilableTimeSlots:'GetInstructorAvaibilableTimeSlots',
    studentAvailabilities: 'StudentAvailabilities',
    instructorAvailabilities: 'InstructorAvailabilities'
  },
  summerCampScheduleSummaries: {
    root: 'SummerCampScheduleSummaries',
    getSummerCampScheduleSummaryForView: 'GetSummerCampScheduleSummaryForView',
    getAvailableInstructors: 'GetAvailableInstructors'
  },
  summerCampEnrollmentDetails: {
    root: 'SummerCampEnrollmentDetails',
    enrollStudent: 'EnrollStudent'
  },
  studentGrades: {
    root: 'StudentGrades',
    getAllByStudentId: 'GetAllByStudentId'
  },
  payment: {
    root: 'Payment',
    cardConnectPayment: 'CardConnectPayment',
    NMIPayment:'NMIPayment',
    getAllCustomerCards:'GetAllCustomerCards',
    deleteNMICustomer:'DeleteNMICustomer',
    paymentUsingSavedCard:'PaymentUsingSavedCard',
    updateNMICustomer:'UpdateNMICustomer',
    setDefaultPaymentMethod:'SetDefaultPaymentMethod',
    addNMICustomer:'AddNMICustomer',
    currentMonthPaymentCalculationForRecurringPlan: 'CurrentMonthPaymentCalculationForRecurringPlan',
    createRecurringSubscription: 'CreateRecurringSubscription',
    getAllBillingDetailsOfUser: 'GetAllBillingDetailsOfUser',
    getAllTransactionsOfUser: 'GetAllTransactionsOfUser',
    singleBillPaymentUsingSavedCard: 'SingleBillPaymentUsingSavedCard',
    getAllPendingTransactionsOfUser: 'GetAllPendingTransactionsOfUser',
    refundPayment: 'RefundPayment',
    applyDiscountOnBill: 'ApplyDiscountOnBill'
  },
  deskManagerDetails: {
    root: 'DeskManagerDetailses'
  },
  classAttendanceAppServices: 'ClassAttendanceAppServices',
  planCancelRequest: {
    root: 'PlanCancelRequest',
    cancelRequest: 'CancelRequest',
    approveRequest: 'ApproveRequest'
  },
  leaveManagement: {
    root: 'LeaveManagement',
    getLeaveRequest: 'GetLeaveRequest',
    leaveApproval: 'LeaveApproval',
    getLeaveBalance: 'GetLeaveBalance',
    getAllSubstituteAvailability: 'getAllSubstituteAvailability',
    createUpdate: 'CreateUpdate',
    getUserEmailDropDown: 'GetUserEmailDropDown',
    deleteLeaveRequest: 'DeleteLeaveRequest',
    getAllSubstituteInstructorForLeaveReqeust: 'GetAllSubstituteInstructorForLeaveReqeust',
    subInstructorAssignment: 'SubInstructorAssignment'
  },
  octopusChatAppServices: {
    root: 'OctopusChatAppServices',
    onConnected: 'OnConnected',
    disConnected: 'DisConnected',
    sendMessage: 'SendMessage',
    chatHistory: 'chatHistory',
    getTotalCountForUnreadMessage: 'GetTotalCountForUnreadMessage',
    markAllAsRead: 'MarkAllAsRead',
    getAllPastMessages: 'GetAllPastMessages',
    deletePastMessage: 'DeletePastMessage'
  },
  user: {
    root: 'User',
    getUsers: 'GetUsers'
  },
  maintenanceRequests: {
    root: 'MaintenanceRequest',
    getAllSupervisorRequests: 'GetAllSupervisorRequests',
    updateRequestStatus: 'UpdateRequestStatus'
  },
  notifications: {
    root: 'Notifications',
    getTotalCountForUnreadNotification: 'GetTotalCountForUnreadNotification',
    getAllNotificationsOfUser: 'GetAllNotifictionsOfUser',
    readNotifications: 'ReadNotifications'
  },
  firebase: {
    root: 'Firebase',
    registerUserToken: 'RegisterUserToken',
    unregisterUserToken: 'UnregisterUserToken',
  },
  storeProduct: {
    root: 'StoreProduct',
    getStoreProductForView: 'GetStoreProductForView',
    getCustomerOrders: 'GetCustomerOrders',
    getAllForDependentView: 'GetAllForDependentView',
    checkoutCartItems: 'CheckoutCartItems',
    paymentForProducts: 'PaymentForProducts',
    getProductCartView: 'GetProductCartView'
  },
  octopusDashBoard: {
    root: 'OctopusDashBoard',
    getDashboardDetails: 'GetDashboardDetails'
  },
  address: {
    root: 'Address',
    getStates: 'GetStates'
  },
  studentNotes: {
    root: 'StudentNotes',
    getAllNotes: 'GetAllNotes'
  },
  instructorNotes: {
    root: 'InstructorNotes',
    getAllNotes: 'GetAllNotes'
  }
};
