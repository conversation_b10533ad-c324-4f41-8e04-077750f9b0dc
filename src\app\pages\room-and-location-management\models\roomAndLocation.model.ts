import { FormArray, FormControl, FormGroup } from '@angular/forms';
import { AdvanceFiltersParams, FilterItem } from '../../scheduler/pages/scheduler-wrapper/pages/schedule/models';

export interface LocationDetails {
  id: number;
  locationName: string;
  address: string;
  locationCode: string;
  rooms: number;
}

export interface SchoolLocations {
  schoolLocations: LocationDetails;
}

export interface RoomInfo {
  id: number;
  roomName: string;
  capacity: string;
  locationId: number;
  instrumentCount: number;
  isVirtualCapable: boolean;
  roomInstrumentList: RoomInstrumentList[];
}

export interface RoomInstrumentList {
  instrumentId: number;
  quantity: number;
  roomId: number;
  instrumentName: string | null;
  id: number;
  instrumentGrade: string;
}

export interface RoomDetails {
  roomDetail: RoomInfo;
  schoolLocationName: string;
  schoolLocationAddress: string;
}

export interface LocationDetailsFormGroup {
  id: FormControl<number | undefined>;
  locationName: FormControl<string>;
  address: FormControl<string>;
  locationCode: FormControl<string>;
  rooms: FormControl<number | undefined>;
}

export interface ACHFormGroup {
  id: FormControl<number | undefined>;
  locationName: FormControl<string>;
  address: FormControl<string>;
  locationCode: FormControl<string>;
  rooms: FormControl<number | undefined>;
  account_type: FormControl<string | undefined>;
  checkType: FormControl<string | undefined>;
  customer_id_type: FormControl<string | undefined>;
}

export interface RoomInstrumentListFormGroup {
  instrumentId: FormControl<number | undefined>;
  quantity: FormControl<number | undefined>;
  roomId: FormControl<number | undefined>;
}

export interface RoomDetailsFormGroup {
  id: FormControl<number | undefined>;
  roomName: FormControl<string>;
  capacity: FormControl<string>;
  locationId: FormControl<number | undefined>;
  isVirtualCapable: FormControl<boolean | undefined>;
  roomInstrumentList: FormArray<FormGroup<RoomInstrumentListFormGroup>>;
}

export interface RoomFilters {
  searchTerm?: string | null;
  locationId: FilterItem;
}

export interface BlockLocationFormGroup {
  id: FormControl<number | undefined>;
  dayOffStartDate: FormControl<string>;
  dayOffEndDate: FormControl<string>;
  reason: FormControl<string>;
  locationIds: FormControl<number[]>;
}

export interface BlockLocationParams {
  locationId: AdvanceFiltersParams;
}

export interface SchoolLocationDayOff {
  id: number;
  dayOffStartDate: string;
  dayOffEndDate: string;
  reason: string;
  locationIds: number[];
  locationNames: string;
  createdDate?: string;
  updatedDate?: string;
}

export enum FiltersEnum {
  ALL_LOCATION = 0
}
