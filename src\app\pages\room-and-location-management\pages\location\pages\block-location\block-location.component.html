<div class="o-sidebar-wrapper">
  <div class="o-sidebar-header">
    <div class="title">{{ selectedLocationDetails ? "Edit " : "Add " }}Unavailable Location</div>
    <div class="action-btn-wrapper">
      <button
        mat-raised-button
        color="accent"
        class="mat-accent-btn back-btn"
        type="button"
        (click)="closeSideNavFun()">
        Close
      </button>
      <button
        mat-raised-button
        color="primary"
        class="mat-primary-btn"
        type="button"
        (click)="onSubmit()"
        [appLoader]="showBtnLoader">
        Save
      </button>
    </div>
  </div>
  <div class="divider"></div>
  <div class="o-sidebar-body">
    <form [formGroup]="locationFormGroup">
      <div class="field-wrapper field-with-mat-inputs">
        <label class="required">Start Date - End Date</label>
        <div class="field-content">
          <mat-form-field class="mat-start-date w-100">
            <input
              matInput
              [matDatepicker]="startPicker"
              (click)="startPicker.open()"
              formControlName="dayOffStartDate"
              (dateChange)="locationFormGroup.controls.dayOffEndDate.reset()"
              placeholder="Select Start Date"
              [min]="maxDate" />
            <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
            <mat-datepicker #startPicker></mat-datepicker>
            <mat-error>
              <app-error-messages [control]="locationFormGroup.controls.dayOffStartDate"></app-error-messages>
            </mat-error>
          </mat-form-field>
            <div class="dash mb-4">-</div>
            <mat-form-field class="mat-start-date w-100">
              <input
                matInput
                [matDatepicker]="endPicker"
                (click)="endPicker.open()"
                formControlName="dayOffEndDate"
                placeholder="Select End Date"
                [min]="
                  locationFormGroup.controls.dayOffStartDate.value
                    ? locationFormGroup.controls.dayOffStartDate.value
                    : maxDate
                " />
              <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
              <mat-datepicker #endPicker></mat-datepicker>
              <mat-error>
                <app-error-messages [control]="locationFormGroup.controls.dayOffEndDate"></app-error-messages>
              </mat-error>
            </mat-form-field>
        </div>
      </div>
      <div class="field-wrapper field-with-mat-inputs">
        <label class="required">Select Location(s)</label>
        <div class="w-100">
          <app-multi-select-chips class="w-100 mat-select-custom" [filterDetail]="blockLocationParams.locationId" (selectedFilterValues)="selectedLocationIds()"></app-multi-select-chips>
          <mat-error>
              <app-error-messages [control]="locationFormGroup.controls.locationIds"></app-error-messages>
          </mat-error>
        </div>
      </div>
      <div class="field-wrapper field-with-mat-inputs">
        <label class="required">Reason</label>
        <mat-form-field>
          <textarea
            matInput
            placeholder="Enter Reason"
            formControlName="reason"
            cdkTextareaAutosize
            cdkAutosizeMinRows="3"
            cdkAutosizeMaxRows="10"></textarea>
          <mat-error>
            <app-error-messages [control]="locationFormGroup.controls.reason"></app-error-messages>
          </mat-error>
        </mat-form-field>
      </div>
    </form>

    <!-- Dotted line divider -->
    <div class="dotted-divider"></div>

    <!-- Location Day Off List Section -->
    <div class="location-day-off-section">
      <div class="section-title">
        <h3>Blocked Locations Details</h3>
      </div>

      <div class="location-day-off-list">
        <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : locationDayOffTemplate"></ng-container>
      </div>
    </div>
  </div>
</div>

<!-- Location Day Off Template -->
<ng-template #locationDayOffTemplate>
  @if (locationDayOffList && locationDayOffList.length > 0) {
    @for (dayOff of locationDayOffList; track dayOff.id; let i = $index) {
      <div class="o-card mb-3 location-day-off-card">
        <div class="o-card-body">
          <div class="card-header">
            <div class="card-number">{{ i + 1 }}.</div>
          </div>
          <div class="day-off-content">
            <div class="content-row">
              <div class="icon-wrapper">
                <img [src]="constants.staticImages.icons.calendarIcon" alt="">
              </div>
              <div class="content-text">
                {{ dayOff.dayOffStartDate | localDate | date }} - {{ dayOff.dayOffEndDate | localDate | date }}
              </div>
            </div>
            <div class="content-row">
              <div class="icon-wrapper">
                <img [src]="constants.staticImages.icons.location" alt="">
              </div>
              <div class="content-text">
                {{ dayOff.locationNames }}
              </div>
            </div>
            <div class="content-row">
              <div class="icon-wrapper">
                <img [src]="constants.staticImages.icons.questionnaire" alt="">
              </div>
              <div class="content-text">
                {{ dayOff.reason }}
              </div>
            </div>
          </div>
                    <div class="edit-icon-wrapper">
              <img [src]="constants.staticImages.icons.editPenGreen" alt="Edit" class="edit-icon" (click)="onEditLocationDayOff(dayOff)">
            </div>
        </div>
      </div>
    }
  } @else {
    <div class="no-data-found-wrapper">
      <h3>No blocked locations found!</h3>
    </div>
  }
</ng-template>

<!-- Loader Template -->
<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>