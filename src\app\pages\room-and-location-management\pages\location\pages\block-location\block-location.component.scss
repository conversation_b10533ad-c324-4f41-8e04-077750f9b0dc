@import "src/assets/scss/variables";
@import "src/assets/scss/theme/_mixins.scss";
 
.field-wrapper {
  @include flex-content-align-center;
  margin-bottom: 20px;
 
  label {
    color: $black-shade-text !important;
    font-size: 16px !important;
    font-weight: 600;
    min-width: 200px;
    margin-bottom: 20px;
  }
 
  .field-content {
    @include flex-content-align-center;
    width: 100%;
 
    .dash {
      margin: 0px 3px;
    }
 
    .dot {
      display: block;
    }
  }
 
  .mat-error-position {
    position: relative;
  }
}
 
.field-with-mat-inputs {
  margin-bottom: 6px;
}
 
::ng-deep {
  .mat-select-custom {
    .mat-mdc-text-field-wrapper {
      height: 35px;
    }

    .mat-mdc-form-field-infix {
      padding-top: 5px !important;
      padding-bottom: 5px !important;
    }

    .mat-mdc-select-placeholder,
    .mat-mdc-select-value-text {
      font-size: 14px !important;
    }

    .multi-select-chips-wrapper {
      padding: 5px 15px !important;

      .select-placeholder {
        color: $gray-text;
        font-weight: normal !important;
      }
    }
  }

  .select-box-options-wrapper {
    .mdc-label {
      color: $black-shade-text !important;
    }
  }

  .mat-mdc-form-field-icon-suffix > .mat-icon {
    padding: 0px 5px 16px 0px !important;
  }

  .mat-drawer-inner-container {
    overflow: hidden !important;
  }

  .o-sidebar-wrapper .o-sidebar-body {
    overflow: auto;
    height: calc(100vh - 68px);
    padding: 20px 30px 10px 30px !important;
  }

  .sidenav-content-without-footer {
    overflow: hidden !important;
  }

  .mat-mdc-form-field-error-wrapper {
    padding: 0 !important;
  }

  .mat-start-date {
    .mat-mdc-text-field-wrapper,
    .mat-mdc-icon-button .mat-mdc-button-touch-target,
    .mat-mdc-button-ripple {
      height: 35px;
    }

    .mat-mdc-icon-button.mat-mdc-button-base {
      height: 35px;
      width: 35px;
    }

    .mat-mdc-icon-button .mat-mdc-button-ripple {
      height: 40px;
    }

    .mat-mdc-form-field-infix {
      padding-top: 5px !important;
      padding-bottom: 5px !important;
    }

    .mat-mdc-select-placeholder,
    .mat-mdc-select-value-text {
      font-size: 14px !important;
    }

    .mat-mdc-icon-button svg {
      height: 16px;
    }

    .mat-mdc-icon-button.mat-mdc-button-base {
      padding: 5px;
    }

    .mat-mdc-icon-button.mat-mdc-button-base {
      top: -9px;
    }

    .mat-datepicker-input {
      font-size: 14px !important;
    }

    .mat-mdc-icon-button .mat-mdc-button-persistent-ripple {
      border-radius: 9%;
    }

    .mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before {
      background: transparent !important;
    }
  }

  .mdc-list-item__primary-text {
    @include w-h-100;
  }
}

// Location Day Off Section Styles
.location-day-off-section {
  margin-top: 20px;

  .section-title {
    margin-bottom: 20px;

    h3 {
      font-size: 18px;
      font-weight: 600;
      color: $black-shade-text;
      margin: 0;
    }
  }

  .location-day-off-list {
    .o-card {
      .day-off-header {
        margin-bottom: 15px;

        .date-range {
          font-size: 16px;
          color: $primary-color;
        }
      }

      .day-off-content {
        .locations,
        .reason {
          margin-bottom: 10px;
          @include flex-content-align-start;
          flex-wrap: wrap;

          .label {
            font-weight: 600;
            color: $black-shade-text;
            min-width: 80px;
            margin-right: 10px;
          }

          .value {
            color: $gray-text;
            flex: 1;
          }
        }

        .reason {
          margin-bottom: 0;
        }
      }
    }

    .no-data-found-wrapper {
      text-align: center;
      padding: 40px 20px;
      color: $gray-text;

      h3 {
        font-size: 16px;
        font-weight: 500;
        margin: 0;
      }
    }

    .page-loader-wrapper {
      text-align: center;
      padding: 40px 20px;
    }
  }
}

@media (max-width: 767px) {
  .action-btn-wrapper {
    display: flex;
  }

  .field-wrapper,
  .field-content {
    flex-wrap: wrap;

    .dash {
      display: none;
    }
  }
}